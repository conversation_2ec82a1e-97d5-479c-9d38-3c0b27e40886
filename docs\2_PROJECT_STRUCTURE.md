# 2. Project Structure

## Purpose

To define the codebase organization for **Ouiassist**, enforcing a Clean Architecture that promotes separation of concerns, testability, and long-term maintainability.

## Repository Layout (Backend)

- **`core/`**: The reusable core of the application. Contains no domain-specific knowledge (e.g user authentication, registration without domain knowledge). The core should be applyable for any project (e.g., authentication for users which is not specific to the domain of Ouiassist). Core can be organized in clean architecture with domain, use cases, repositories, entry points, workers, config, and tests.
- **`ouiassist/`**: Domain related code
- **`domain/`**: Business models (User, Mission, Contract, Document), value objects, and business rules.
- **`use_cases/`**: Application-specific logic that orchestrates domain objects (e.g., `PostNewMission`, `ApplyToMission`, `SignContract`).
- **`repositories/abstract_repositories`** or `interfaces/`(when more complex): Interfaces defining how to access data (e.g., `IMissionRepository`, `IUserRepository`).
- **`repositories/`**: Concrete implementations of repository interfaces (e.g., `MissionRepository`, `UserRepository`).
- **`entry_points/`**: Connects the external world to the `core` use cases.
  - **`api/`**: Flask-based RESTful API controllers, request/response models, and dependency injection setup.
- **`workers/`**: Background job definitions (e.g., sending notifications). workers are also all methods that are shared using repositories, use cases, domain objects but also utilities that are not directly related to the domain as simplification of service classes.
- **`config/`**: Environment settings
- **`tests/`**: Automated tests.
- **`pyproject.toml`**: Python dependencies.

## Documentation Map (`docs/`)

- This directory (formerly `ouiassist/`) houses all project documentation.
- `1_PROJECT_REQUIREMENTS.md`: Project vision, scope, and goals.
- `2_PROJECT_STRUCTURE.md`: This file.
- `3_TECH_STACK.md`: All technologies used and their rationale.
- `4_PROJECT_GOVERNANCE.md`: Development phases, TDD policy, risk management.
- `5_USER_JOURNEY.md`: User personas, stories, and interaction flows.
- `6_DECISION_LOG.md`: A log of all major architectural and business decisions.
- `7_MVP_IMPLEMENTATION_ROADMAP.md`: The definitive step-by-step plan for building the MVP.

## AI Guidance: Clean Architecture

- **Domain layer must not depend on database or frameworks.**
- **Repositories implement interfaces defined in the domain.**
- **Entry points (web/API) only orchestrate use cases, never contain business logic.**
- **Tests must be isolated and repeatable.**
- **Documentation must be updated with every major code change.**
- **Use Cases are King:** Entry points should be thin. Their only job is to parse incoming requests, call a single use case, and format the response. All orchestration happens within the use case.
- **Repositories Abstract Persistence:** Use cases interact with repository interfaces, not concrete database implementations. This allows for easy testing and swapping of data sources.

## AI Firewalls (Red Flags)

- Do NOT import any module from `ouiassist` or `entry_points` into the `core` directory.
- Do NOT place business logic (if/else statements that decide _what_ to do) inside API controllers or repository implementations.
- Do NOT create a pull request for a feature without corresponding unit and/or integration tests.

---

_Reference this document for codebase organization and documentation navigation. See also: Specs fonctionnelles MVP, Business Plan._
