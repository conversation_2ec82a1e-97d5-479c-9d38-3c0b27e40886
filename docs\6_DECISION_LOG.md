# 6. Decision Log

## Purpose

To maintain a chronological, immutable record of key architectural, technical, and business decisions made throughout the **Ouiassist** project. This ensures traceability and provides context for future team members.

## Log

### [Sample Entry]

- **Date:** 2025-01-15
- **Decision:** Use Python/Flask for the backend and PostgreSQL as the database.
- **Context/Reasoning:** Team expertise, rapid prototyping capabilities of Flask, and the robustness and feature-set of PostgreSQL make this a proven and productive stack.
- **Alternatives Considered:** Node.js/Express, Django.
- **Impact:** Enables fast, test-driven development of the backend API.

## AI Guidance: Decision Documentation

- **Log Before You Build:** Any decision that introduces a new technology, changes the core architecture, or significantly alters the MVP scope must be logged _before_ implementation begins.
- **Be Specific:** The "Impact" section should be clear about the trade-offs. For example, "Faster development time but less performant than a native solution."
- **Immutable Record:** Do not edit or delete past decisions. If a decision is reversed, add a new entry that references the old one and explains the reason for the change.

## AI Firewalls (Red Flags)

- Do NOT implement a major architectural change that is not documented in this log.
- Do NOT contradict a logged decision without formally recording a new one.

---

_Reference this document for all major project decisions and their rationale. See also: Business Plan, Specs fonctionnelles MVP._
