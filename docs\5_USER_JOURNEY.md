# 5. User Journey & Experience

## Purpose

To describe the end-to-end experience for **Ouiassist** users, ensuring that every feature is designed with a deep understanding of their context, motivations, and pain points.

## Personas

- **The Vacataire: Dr. <PERSON><PERSON><PERSON><PERSON><PERSON>**
  - **Role:** General Practitioner (2 years experience)
  - **Goal:** Find flexible, short-term missions to earn income and explore different clinical environments before committing to a full-time position.
  - **Pain Points:** "I hear about opportunities too late through friends. I'm also worried about not getting paid on time and have no formal contract for short gigs."
- **The Employer: Mme. <PERSON> Sarr**
  - **Role:** Clinic Administrator
  - **Goal:** Quickly and reliably find qualified nurses or doctors to cover unexpected absences or temporary peaks in activity.
  - **Pain Points:** "It takes me half a day of calling people to find someone available. Even then, I'm not always sure of their credentials until they show up. The paperwork is a nightmare."

## Core User Stories (MVP)

- **Registration & Verification:**
  - As Dr. <PERSON>, I want to create a profile by uploading my ID card, medical degree, and license from the _Ordre des Médecins_, so that my profile can be "Verified" and I can start applying for missions.
- **Mission Discovery:**
  - As Dr. <PERSON>, I want to search for missions based on location (e.g., "Dakar"), specialty ("General Medicine"), and date, so I can find opportunities that fit my schedule.
- **Mission Posting:**
  - As Mme. Sarr, I want to post a new mission for a "Registered Nurse" for 3 night shifts next week, specifying the required skills and proposed budget, so that qualified candidates are notified immediately.
- **Application & Selection:**
  - As Mme. Sarr, I want to review the profiles of applicants for my mission, see their verified documents and past ratings, so I can confidently select the best candidate.
- **Contracting & Confirmation:**
  - As both Dr. Diallo and Mme. Sarr, I want to review and e-sign a standardized digital contract for the mission, so that all terms (duties, hours, pay rate) are clear and legally binding.
- **Mission Execution & Payment:**
  - As Dr. Diallo, after completing my shifts, I want to submit a digital timesheet through the app, so that Mme. Sarr can approve it and my payment is processed automatically.
- **Feedback Loop:**
  - As Mme. Sarr, after the mission is complete and payment is sent, I want to rate Dr. Diallo on her professionalism and skills, so that good work is rewarded and the platform maintains a high standard of quality.

## User Journey Maps

#### 1. Vacataire: From Registration to Payment

`Sign Up` -> `Complete Profile` -> `Upload & Await Document Verification` -> `Profile Verified!` -> `Search/Filter Missions` -> `Apply to Mission` -> `Get Accepted` -> `Receive & E-sign Contract` -> `Perform Mission` -> `Submit Timesheet` -> `Get Timesheet Approved` -> `Receive Payment` -> `Rate Employer`

#### 2. Employer: From Need to Completion

`Sign Up` -> `Create Facility Profile` -> `Post New Mission` -> `Receive Applications` -> `Review & Compare Candidates` -> `Select & Offer Mission` -> `Candidate Accepts & E-signs Contract` -> `Mission is Performed` -> `Receive & Approve Timesheet` -> `Authorize Payment` -> `Rate Vacataire`

## AI Guidance: User-Centric Design

- **Mobile-First:** All UI designs must be responsive and work flawlessly on mobile browsers, as this will be the primary access device for most users.
- **Clarity and Trust:** The UI must constantly reinforce security and trust. Use visual cues for "Verified" documents, secure payments, and transparent statuses.
- **Minimize Friction:** Automate as much as possible. Pre-fill forms, use clear language (FR/EN), and provide helpful tooltips to guide users through complex steps like document upload and contract signing.

## AI Firewalls (Red Flags)

- Do NOT design a feature that does not map directly to one of the core user stories.
- Do NOT assume users understand complex jargon. Use simple, direct language.
- Do NOT launch a flow (e.g., payments) without extensive user testing to ensure it is intuitive and trustworthy.

---

_Reference this document for user flows, stories, and experience design. See also: Specs fonctionnelles MVP, Business Plan._
