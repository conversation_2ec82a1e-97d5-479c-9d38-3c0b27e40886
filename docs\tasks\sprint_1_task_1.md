# Sprint 1 - Task B1.1: Model User and Role Domains

## Task Overview
**Task ID**: Task-B1.1
**Sprint**: 1 - User Identity & Authentication
**Priority**: High
**Estimated Effort**: 5-7 hours

## Objective
Create a modular user domain model with two layers:
1. Core domain layer with generic User and Role entities that can be reused across different business contexts
2. Specialized domain layer for healthcare-specific user types (Professional and Business)

This task establishes the foundation for the Ouiassist platform's user management system following hexagonal architecture principles with comprehensive typing and Protocol-based interfaces.

## Scope
This task focuses on modeling both generic and healthcare-specific user domain entities with their associated value objects, business rules, and domain events. It does NOT include:
- Database persistence implementation
- API endpoints
- Authentication logic (covered in Task-B1.2)
- Use cases (covered in subsequent tasks)

## Business Context
Based on the project requirements, the platform serves two primary user types:
- **Professional**: Healthcare professionals seeking temporary work opportunities
- **Business**: Healthcare facilities needing temporary staff

The User domain must support:
- Secure identity management with a reusable core model
- Profile information specific to healthcare roles
- Document verification status tracking
- Role-based access control

## Technical Requirements

### Architecture Principles
- Follow hexagonal (clean) architecture
- Core domain layer must be business-agnostic
- Specialized domain layer extends core with healthcare-specific logic
- Use Protocol from typing instead of abc for interfaces
- Comprehensive typing throughout all code
- Domain-driven design principles
- Immutable value objects where appropriate
- Self-documenting code with minimal docstrings
- No inline comments

### Dependencies
- Python 3.11+
- dataclasses for entity modeling
- typing.Protocol for interfaces
- uuid for entity identification
- datetime for temporal tracking
- enum for role definitions

## Implementation Plan

### Step 1: Define Core Domain Entities and Value Objects

**Files to create:**
- `core/domain/user.py` (User entity, user-related value objects, events, and status enums)
- `core/domain/role.py` (Role entity and permission model)

**Implementation details:**

- In `user.py`:
  - User entity with core properties
  - Email, PhoneNumber, and PersonName value objects with validation
  - User status enumerations (ACTIVE, INACTIVE, SUSPENDED)
  - Verification status enumerations (PENDING, VERIFIED, REJECTED)
  - Domain events (UserRegistered, UserStatusChanged, UserProfileUpdated)
  - Core user business rules and validation methods

- In `role.py`:
  - Role entity with permissions
  - Permission validation logic

### Step 2: Define Healthcare-Specific Domain Model

**Files to create:**
- `domain/professional.py` (healthcare professional user type)
- `domain/business.py` (healthcare business user type, facility details)
- `domain/qualification.py` (professional qualifications)
- `domain/user_type.py` (healthcare-specific user type enumerations)

**Implementation details:**
- In `professional.py`:
  - Professional class extending core User
  - Professional qualifications management
  - Specialties handling
  - Availability scheduling
  - Verification workflow methods
  - Domain events for professional-specific state changes

- In `business.py`:
  - Business class extending core User
  - Facility details management
  - Staff requirements specification
  - Business verification requirements
  - Domain events for business-specific state changes

- In `user_type.py`:
  - UserType enumeration (PROFESSIONAL, BUSINESS)

- In `qualification.py`:
  - Specialty value object
  - Qualification value object with validation methods
  - DocumentType enumeration

### Step 3: Define Repository Interfaces

**Files to create:**
- `core/repositories/user_repository.py`
- `core/repositories/role_repository.py`
- `repositories/professional_repository.py`
- `repositories/business_repository.py`

**Implementation details:**
- Core UserRepository protocol
- Core RoleRepository protocol
- Professional and Business repository protocols extending core UserRepository

### Step 4: Create Worker Methods

**Implementation details:**
- User validation methods embedded in User class
- User authentication helpers as module-level functions in `user.py`
- Professional-specific worker methods in `professional.py`
- Business-specific worker methods in `business.py`
- Common utilities as module-level functions in appropriate files

## Test Requirements (TDD Approach)

### Test Files to Create
- Core domain tests
- Healthcare-specific domain tests

### Test Categories
1. **Core Value Object Tests**
2. **Core User Entity Tests**
3. **Core Role Entity Tests**
4. **Healthcare-Specific Entity Tests**
5. **Worker Method Tests**

### Test Factories
Create test factories for both core and healthcare-specific entities

## Acceptance Criteria

### Functional Requirements
- [ ] Core User entity can be created with all required fields
- [ ] Healthcare-specific User types extend the core User with specialized data
- [ ] User verification workflows follow healthcare-specific business rules
- [ ] Role entity supports permission-based access control
- [ ] Value objects enforce data integrity constraints
- [ ] Domain events are properly generated for state changes

### Technical Requirements
- [ ] Clear separation between core and healthcare-specific domains
- [ ] All code is fully typed with mypy compliance
- [ ] Repository interfaces use Protocol instead of abc
- [ ] Domain layers have no external dependencies
- [ ] All business rules are enforced in the appropriate domain layer
- [ ] Code is self-documenting with meaningful names
- [ ] Docstrings are minimal and focused on "why" not "what" and is only for public APIs
- [ ] No inline comments
- [ ] Comprehensive test coverage (>90%)
- [ ] All tests pass with pytest
- [ ] Code passes ruff linting checks

### Documentation Requirements
- [ ] Minimal but essential docstrings for public APIs
- [ ] Business rules are clearly represented in code
- [ ] Domain events are properly typed with descriptive names
- [ ] Repository interfaces are self-explanatory
- [ ] The relationship between core and healthcare domains is evident from code organization

## Definition of Done
- [ ] All implementation steps completed
- [ ] All tests written and passing (TDD approach)
- [ ] Code review completed
- [ ] mypy type checking passes
- [ ] ruff linting passes
- [ ] Documentation updated
- [ ] No breaking changes to existing code
- [ ] Ready for Task-B1.2 (SuperTokens integration)

## Notes
- The core domain establishes reusable user management concepts
- The healthcare domain extends the core with specialized business rules
- Focus on domain purity - no infrastructure concerns
- Ensure value objects are immutable where appropriate
- Use clear, descriptive naming that eliminates the need for comments
- Domain events are implemented as methods within entity classes
- Worker methods replace the service layer for simplicity

