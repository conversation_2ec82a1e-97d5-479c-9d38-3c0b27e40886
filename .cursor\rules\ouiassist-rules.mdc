---
description:
globs:
alwaysApply: true
---
# Cursor Rules for VacAssist/OuiLink

## Purpose
These rules enforce clean code and clean architecture for the VacAssist/OuiLink project. They serve as guidelines and firewalls for AI assistants and developers, ensuring maintainability, scalability, and security. All rules are cross-referenced with the documentation in `ouiassist/`.

---

## 1. Directory & Layer Boundaries
- Domain logic must reside in `core/domain/` only. No infrastructure or framework code here.
  _See: 2_PROJECT_STRUCTURE.md, AI Guidance_
- Data access and persistence logic must be in `core/repositories/` and must implement interfaces from `core/interfaces/`.
- Web/API entry points must be in `entry_points/` and must not contain business logic.
- Configuration files must be in `config/`.
- Documentation and guidelines must be in `ouiassist/`.

## 2. Naming Conventions
- Classes: `CamelCase` (e.g., `UserRepository`)
- Functions/methods: `snake_case` (e.g., `find_by_id`)
- Constants: `UPPER_SNAKE_CASE`
- Files: `lower_snake_case.py`
- Test files: `test_*.py` in the appropriate test directory
- Test function names: `test_function_name_should_expected_behaviour` (e.g., `test_login_should_return_token_on_valid_credentials`)
  - Rationale: Explicit, meaningful test names improve clarity and maintainability.

_See: 2_PROJECT_STRUCTURE.md, 3_TECH_STACK.md_

## 3. Code Style & Standards
- All code must comply with PEP8.
- Type hints are required for all functions and methods.
- Docstrings and inline comments should be present only when extremely necessary. Code should self-document most of the time.
- No hardcoded credentials or sensitive data.
- Use environment variables for configuration.

_See: 1_PROJECT_REQUIREMENTS.md, 3_TECH_STACK.md_

## 4. Test Coverage & TDD
- Development is strictly test-driven (TDD): always write unit tests first, ensure they pass, then write integration tests to validate changes did not break current behaviour.
- Every feature and bugfix must include or update tests.
- All business logic must be covered by unit tests in `core/tests/`.
- Integration tests must be provided for API endpoints.
- No code is merged without passing tests.
- Always write tests first (TDD principle).
- Tests using domain data should use factories method that create domain data when they are simple dataclasses that carry just data.
- Test names must be meaningful and explicit, following the pattern `test_function_name_should_expected_behaviour`.
- Cover all conditional cases in tests, but avoid stress/unnecessary tests (e.g., trivial initializations).
  - Rationale: TDD ensures reliability and maintainability; meaningful tests prevent regressions and clarify intent.

_See: 4_IMPLEMENTATION_PLAN.md, 7_TRACKING_PLANNING.md_

## 5. Prohibited Patterns (AI Firewalls)
- No business logic in `entry_points/` or `repositories/`.
- No direct database or API calls from UI or controller layers.
- No raw SQL in business logic; always use ORM.
- No unapproved libraries or frameworks.
- No skipping of validation, error handling, or documentation.
- No features outside the defined MVP scope without explicit instruction.
- No code without corresponding documentation and tests.
- AI must never merge or make pull requests automatically; always ask for approval when a feature is done and ready for review.
  - Rationale: Human review is required for all merges to ensure quality and accountability.

_See: AI Firewalls in all markdown docs_

## 6. Documentation & Traceability
- Every major technical or business decision must be logged in `6_DECISION_LOG.md`.
- All new modules and APIs must be documented in the appropriate markdown file.
- Documentation must be updated with every major code or architecture change.

_See: 6_DECISION_LOG.md, 2_PROJECT_STRUCTURE.md_

## 7. Use of Repositories, Use Cases, and Workers
- Use repositories and use cases as in clean architecture; avoid 'services' in favor of worker methods.
- Worker methods should encapsulate business processes or background jobs, not generic service classes.
  - Rationale: This reduces unnecessary complexity and aligns with clean architecture principles.

## 8. Review & Enforcement
- All pull requests must be reviewed for compliance with these rules.
- AI assistants must reference these rules and the markdown docs before generating or modifying code.
- AI must always ask for approval before making a pull request or merge, never proceed automatically.

---







_These rules are mandatory for all contributors and AI assistants. For rationale and examples, see the markdown documentation in `ouiassist/`.