# 3. Technology Stack

## Purpose

To list and justify the technologies chosen for **Ouiassist**, prioritizing security, scalability, developer productivity, and suitability for the Senegalese market.

## Backend

- **Python Flask**: Rapid backend development, strong ecosystem, easy to test and maintain
- **Pytest**: Comprehensive testing framework for TDD
- **ruff (isort, black, flake8)**: Consistent, high-quality code formatting and linting
- **mypy**: Static type checking for Python
- **uv**: An extremely fast Python package and project manager, written in Rust.
- **SQLAlchemy ORM**: Flexible, database-agnostic persistence layer
- **Auth: SuperTokens**: Secure, scalable identity management, easy to deploy locally with docker

## Frontend

- **Vue.js**: Modern, responsive UI, easy integration with backend APIs
- **SuperTokens Prebuilt UI**: Fast, secure authentication flows
- **Vuetify**: Material Design component library for Vue.js

## Database

- **PostgreSQL**: Robust, scalable relational database, well-supported in Africa

## Rationale

- Python and Flask enable rapid prototyping and clean code
- Vue.js offers a lightweight, modern frontend experience
- PostgreSQL is reliable and cost-effective for healthcare data
- SuperTokens ensures secure authentication and session management

## AI Guidance: Technology Use

- **Always use ORM for database access; never raw SQL in business logic.**
- **Authentication must use SuperTokens; do not roll your own.**
- **Frontend and backend must communicate via RESTful APIs.**
- **All third-party dependencies must be listed in `requirements.txt` and justified.**

## AI Firewalls (Red Flags)

- Do NOT use unapproved libraries or frameworks.
- Do NOT bypass ORM for direct database access.
- Do NOT implement authentication or authorization from scratch.
- Do NOT store sensitive data without encryption.
- All code should be typed using Python's type hinting.
- ruff should be used after every implementation to ensure code quality.
- factory methods should be used for creating objects in tests.
- imports should be always on top of the file.

---

_Reference this document for technology choices and setup. See also: Specs fonctionnelles MVP, Business Plan._
